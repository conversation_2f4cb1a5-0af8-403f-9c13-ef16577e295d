# Production Deployment Checklist

## Pre-Deployment
- [ ] Copy .env.production to .env.local on server
- [ ] Update all environment variables with production values
- [ ] Generate secure JWT secret
- [ ] Configure production database
- [ ] Set up production email SMTP
- [ ] Configure payment gateway keys

## Files to Upload
- [ ] .next/ folder (build output)
- [ ] public/ folder (static assets)
- [ ] package.json
- [ ] next.config.js
- [ ] .env.local (from .env.production template)
- [ ] rainbow_paws_production.sql (database)

## Server Setup
- [ ] Node.js 18+ installed
- [ ] MySQL database created
- [ ] Import database: mysql -u user -p database < rainbow_paws_production.sql
- [ ] Install dependencies: npm install --production
- [ ] Start application: npm start

## Post-Deployment Testing
- [ ] Application loads correctly
- [ ] User registration works
- [ ] Email notifications work
- [ ] Payment processing works
- [ ] Admin panel accessible
- [ ] Database operations work

## Security
- [ ] HTTPS enabled
- [ ] SSL certificate installed
- [ ] Firewall configured
- [ ] Database secured
- [ ] Environment variables secured

Generated on: 2025-07-30T10:40:41.324Z
