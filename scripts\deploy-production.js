#!/usr/bin/env node

/**
 * Production Deployment Preparation Script
 * This script prepares the application for production deployment
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

async function prepareProduction() {
  console.log('🚀 Preparing RainbowPaws for production deployment...\n');

  try {
    // 1. Check if required files exist
    console.log('📋 Checking required files...');
    const requiredFiles = [
      'package.json',
      'next.config.js',
      '.env.production',
      'DEPLOYMENT.md'
    ];

    for (const file of requiredFiles) {
      try {
        await fs.access(file);
        console.log(`✅ ${file} exists`);
      } catch {
        console.log(`❌ ${file} missing`);
        throw new Error(`Required file ${file} is missing`);
      }
    }

    // 2. Run production build
    console.log('\n🔨 Running production build...');
    try {
      execSync('npm run build', { stdio: 'inherit' });
      console.log('✅ Production build completed successfully');
    } catch (error) {
      throw new Error('Production build failed');
    }

    // 3. Export database
    console.log('\n💾 Exporting database...');
    try {
      const { exportDatabase } = require('./export-database.js');
      await exportDatabase();
      console.log('✅ Database exported successfully');
    } catch (error) {
      console.log('⚠️  Database export failed:', error.message);
      console.log('   You may need to export manually using mysqldump');
    }

    // 4. Create deployment package info
    console.log('\n📦 Creating deployment package info...');
    const packageInfo = {
      name: 'RainbowPaws',
      version: '1.0.0',
      buildDate: new Date().toISOString(),
      nodeVersion: process.version,
      environment: 'production',
      files: {
        required: [
          '.next/',
          'public/',
          'package.json',
          'next.config.js',
          '.env.local (create from .env.production)',
          'rainbow_paws_production.sql'
        ],
        optional: [
          'DEPLOYMENT.md',
          'README.md'
        ]
      },
      commands: {
        install: 'npm install --production',
        start: 'npm start',
        port: 3000
      }
    };

    await fs.writeFile('deployment-info.json', JSON.stringify(packageInfo, null, 2));
    console.log('✅ Deployment info created');

    // 5. Create production checklist
    console.log('\n📝 Creating production checklist...');
    const checklist = `# Production Deployment Checklist

## Pre-Deployment
- [ ] Copy .env.production to .env.local on server
- [ ] Update all environment variables with production values
- [ ] Generate secure JWT secret
- [ ] Configure production database
- [ ] Set up production email SMTP
- [ ] Configure payment gateway keys

## Files to Upload
- [ ] .next/ folder (build output)
- [ ] public/ folder (static assets)
- [ ] package.json
- [ ] next.config.js
- [ ] .env.local (from .env.production template)
- [ ] rainbow_paws_production.sql (database)

## Server Setup
- [ ] Node.js 18+ installed
- [ ] MySQL database created
- [ ] Import database: mysql -u user -p database < rainbow_paws_production.sql
- [ ] Install dependencies: npm install --production
- [ ] Start application: npm start

## Post-Deployment Testing
- [ ] Application loads correctly
- [ ] User registration works
- [ ] Email notifications work
- [ ] Payment processing works
- [ ] Admin panel accessible
- [ ] Database operations work

## Security
- [ ] HTTPS enabled
- [ ] SSL certificate installed
- [ ] Firewall configured
- [ ] Database secured
- [ ] Environment variables secured

Generated on: ${new Date().toISOString()}
`;

    await fs.writeFile('PRODUCTION-CHECKLIST.md', checklist);
    console.log('✅ Production checklist created');

    // 6. Summary
    console.log('\n🎉 Production preparation completed!\n');
    console.log('📁 Files ready for deployment:');
    console.log('   ├── .next/ (build output)');
    console.log('   ├── public/ (static files)');
    console.log('   ├── package.json');
    console.log('   ├── next.config.js');
    console.log('   ├── .env.production (template)');
    console.log('   ├── rainbow_paws_production.sql (database)');
    console.log('   ├── DEPLOYMENT.md (guide)');
    console.log('   ├── PRODUCTION-CHECKLIST.md (checklist)');
    console.log('   └── deployment-info.json (package info)\n');

    console.log('📋 Next steps:');
    console.log('   1. Choose your hosting platform (see DEPLOYMENT.md)');
    console.log('   2. Upload the required files');
    console.log('   3. Configure environment variables');
    console.log('   4. Set up the database');
    console.log('   5. Follow the production checklist\n');

    console.log('🔗 Recommended hosting platforms:');
    console.log('   • Vercel (recommended for Next.js)');
    console.log('   • Railway (full-stack hosting)');
    console.log('   • Render (simple deployment)');
    console.log('   • Traditional web hosting with Node.js support\n');

  } catch (error) {
    console.error('❌ Error preparing production:', error.message);
    process.exit(1);
  }
}

// Run the preparation
if (require.main === module) {
  prepareProduction();
}

module.exports = { prepareProduction };
