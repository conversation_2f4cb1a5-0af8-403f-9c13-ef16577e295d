# RainbowPaws Deployment Guide

## Pre-Deployment Checklist

### 1. Environment Configuration
- [ ] Copy `.env.production` to `.env.local` on your production server
- [ ] Update all environment variables with production values
- [ ] Generate secure JWT secret: `openssl rand -base64 32`
- [ ] Configure production database credentials
- [ ] Set up production SMTP email settings
- [ ] Configure production payment gateway keys

### 2. Database Setup
- [ ] Export your local database: `mysqldump -u root -p rainbow_paws > rainbow_paws_production.sql`
- [ ] Create production database
- [ ] Import database to production server
- [ ] Update database connection settings in `.env.local`

### 3. Build Application
```bash
npm install
npm run build
npm run start
```

## Hosting Options

### Option 1: Vercel (Recommended for Next.js)

**Pros:**
- Built specifically for Next.js
- Automatic deployments from Git
- Serverless functions
- Free tier available
- Automatic HTTPS

**Setup:**
1. Push your code to GitHub/GitLab
2. Connect your repository to Vercel
3. Set environment variables in Vercel dashboard
4. Deploy database separately (PlanetScale, Railway, etc.)

**Database Options for Vercel:**
- PlanetScale (MySQL-compatible)
- Railway
- AWS RDS
- Google Cloud SQL

### Option 2: Railway

**Pros:**
- Can host both app and database
- Simple deployment
- Built-in database hosting

**Setup:**
1. Connect GitHub repository
2. Add MySQL database service
3. Configure environment variables
4. Deploy

### Option 3: Render

**Pros:**
- Full-stack hosting
- Database included
- Automatic SSL

**Setup:**
1. Connect GitHub repository
2. Create PostgreSQL/MySQL database
3. Configure environment variables
4. Deploy

### Option 4: Traditional Web Hosting (cPanel)

**Requirements:**
- Node.js support (version 18+)
- MySQL database
- SSH access (recommended)

**Setup:**
1. Upload files via FTP/SSH
2. Install dependencies: `npm install --production`
3. Build application: `npm run build`
4. Configure database
5. Start application: `npm start`

## Production Configuration

### Security Checklist
- [ ] Use HTTPS for all URLs
- [ ] Generate strong passwords and secrets
- [ ] Enable security headers
- [ ] Configure CORS properly
- [ ] Set up rate limiting
- [ ] Enable database SSL if available

### Performance Optimization
- [ ] Enable image optimization
- [ ] Configure CDN if needed
- [ ] Set up caching headers
- [ ] Optimize database queries
- [ ] Monitor application performance

## Database Migration

### Export Local Database
```bash
mysqldump -u root -p rainbow_paws > rainbow_paws_backup.sql
```

### Import to Production
```bash
mysql -u production_user -p production_database < rainbow_paws_backup.sql
```

## Environment Variables Setup

Copy these to your hosting platform's environment variables:

```
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://yourdomain.com
DB_HOST=your-db-host
DB_USER=your-db-user
DB_PASSWORD=your-db-password
DB_NAME=rainbow_paws
JWT_SECRET=your-secure-jwt-secret
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
PAYMONGO_SECRET_KEY=your-paymongo-key
TWILIO_ACCOUNT_SID=your-twilio-sid
```

## Post-Deployment Testing

### Functionality Tests
- [ ] User registration and login
- [ ] Email notifications
- [ ] SMS notifications
- [ ] Payment processing
- [ ] File uploads
- [ ] Database operations
- [ ] Admin panel access

### Performance Tests
- [ ] Page load times
- [ ] Database query performance
- [ ] Image loading
- [ ] Mobile responsiveness

## Troubleshooting

### Common Issues
1. **Database Connection Errors**
   - Check database credentials
   - Verify database server is running
   - Check firewall settings

2. **Environment Variable Issues**
   - Ensure all required variables are set
   - Check for typos in variable names
   - Verify secrets are properly escaped

3. **Build Errors**
   - Check Node.js version compatibility
   - Verify all dependencies are installed
   - Review build logs for specific errors

4. **Email/SMS Not Working**
   - Verify SMTP/Twilio credentials
   - Check spam folders
   - Test with different email providers

## Monitoring and Maintenance

### Regular Tasks
- [ ] Monitor application logs
- [ ] Check database performance
- [ ] Update dependencies regularly
- [ ] Backup database regularly
- [ ] Monitor SSL certificate expiration
- [ ] Review security logs

### Backup Strategy
- Daily database backups
- Weekly full application backups
- Test restore procedures regularly

## Support

For deployment assistance:
1. Check application logs
2. Review hosting provider documentation
3. Test locally first
4. Contact hosting support if needed
