#!/usr/bin/env node

/**
 * Database Export Script for Production Deployment
 * This script exports the database structure and data for production deployment
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

async function exportDatabase() {
  let connection;
  
  try {
    console.log('🔄 Connecting to database...');
    
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'rainbow_paws',
      port: process.env.DB_PORT || 3306
    });

    console.log('✅ Connected to database successfully');

    // Get all tables
    const [tables] = await connection.execute('SHOW TABLES');
    const tableNames = tables.map(row => Object.values(row)[0]);

    console.log(`📊 Found ${tableNames.length} tables:`, tableNames.join(', '));

    let sqlDump = '';
    
    // Add header
    sqlDump += `-- RainbowPaws Database Export\n`;
    sqlDump += `-- Generated on: ${new Date().toISOString()}\n`;
    sqlDump += `-- Database: ${process.env.DB_NAME}\n\n`;
    
    sqlDump += `SET FOREIGN_KEY_CHECKS = 0;\n`;
    sqlDump += `SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";\n`;
    sqlDump += `SET AUTOCOMMIT = 0;\n`;
    sqlDump += `START TRANSACTION;\n\n`;

    // Export each table
    for (const tableName of tableNames) {
      console.log(`📋 Exporting table: ${tableName}`);
      
      // Get table structure
      const [createTable] = await connection.execute(`SHOW CREATE TABLE \`${tableName}\``);
      sqlDump += `-- Table structure for \`${tableName}\`\n`;
      sqlDump += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
      sqlDump += createTable[0]['Create Table'] + ';\n\n';
      
      // Get table data
      const [rows] = await connection.execute(`SELECT * FROM \`${tableName}\``);
      
      if (rows.length > 0) {
        sqlDump += `-- Data for table \`${tableName}\`\n`;
        
        // Get column names
        const [columns] = await connection.execute(`SHOW COLUMNS FROM \`${tableName}\``);
        const columnNames = columns.map(col => `\`${col.Field}\``).join(', ');
        
        sqlDump += `INSERT INTO \`${tableName}\` (${columnNames}) VALUES\n`;
        
        const values = rows.map(row => {
          const rowValues = Object.values(row).map(value => {
            if (value === null) return 'NULL';
            if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
            if (value instanceof Date) return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
            return value;
          });
          return `(${rowValues.join(', ')})`;
        });
        
        sqlDump += values.join(',\n') + ';\n\n';
      }
    }
    
    sqlDump += `SET FOREIGN_KEY_CHECKS = 1;\n`;
    sqlDump += `COMMIT;\n`;

    // Save to file
    const exportPath = path.join(process.cwd(), 'rainbow_paws_production.sql');
    await fs.writeFile(exportPath, sqlDump);
    
    console.log(`✅ Database exported successfully to: ${exportPath}`);
    console.log(`📁 File size: ${(await fs.stat(exportPath)).size} bytes`);
    
    // Create a structure-only export as well
    let structureOnly = '';
    structureOnly += `-- RainbowPaws Database Structure Only\n`;
    structureOnly += `-- Generated on: ${new Date().toISOString()}\n\n`;
    structureOnly += `SET FOREIGN_KEY_CHECKS = 0;\n\n`;
    
    for (const tableName of tableNames) {
      const [createTable] = await connection.execute(`SHOW CREATE TABLE \`${tableName}\``);
      structureOnly += `-- Table structure for \`${tableName}\`\n`;
      structureOnly += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
      structureOnly += createTable[0]['Create Table'] + ';\n\n';
    }
    
    structureOnly += `SET FOREIGN_KEY_CHECKS = 1;\n`;
    
    const structurePath = path.join(process.cwd(), 'rainbow_paws_structure.sql');
    await fs.writeFile(structurePath, structureOnly);
    
    console.log(`✅ Database structure exported to: ${structurePath}`);

  } catch (error) {
    console.error('❌ Error exporting database:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the export
if (require.main === module) {
  exportDatabase();
}

module.exports = { exportDatabase };
