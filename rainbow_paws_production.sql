-- RainbowPaws Database Export
-- Generated on: 2025-07-30T10:40:40.702Z
-- Database: rainbow_paws

SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;

-- Table structure for `admin_logs`
DROP TABLE IF EXISTS `admin_logs`;
CREATE TABLE `admin_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL,
  `action` varchar(100) NOT NULL,
  `entity_type` varchar(50) NOT NULL,
  `entity_id` int(11) NOT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  <PERSON><PERSON>Y `admin_id` (`admin_id`),
  <PERSON><PERSON>Y `entity_type` (`entity_type`,`entity_id`),
  KEY `action` (`action`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `admin_logs`
INSERT INTO `admin_logs` (`id`, `admin_id`, `action`, `entity_type`, `entity_id`, `details`, `ip_address`, `created_at`) VALUES
(32, 0, 'restrict_business', 'service_providers', 1, '{"action":"restrict","businessId":1,"newStatus":"restricted","newApplicationStatus":"restricted"}', NULL, '2025-07-14 13:06:45'),
(33, 0, 'restrict_business', 'service_providers', 1, '{"action":"restrict","businessId":1,"newStatus":"restricted","newApplicationStatus":"restricted"}', NULL, '2025-07-14 17:41:04'),
(34, 0, 'restore_business', 'service_providers', 1, '{"action":"restore","businessId":1,"newStatus":"verified","newApplicationStatus":"approved"}', NULL, '2025-07-14 17:41:10');

-- Table structure for `admin_notifications`
DROP TABLE IF EXISTS `admin_notifications`;
CREATE TABLE `admin_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `entity_type` varchar(50) DEFAULT NULL,
  `entity_id` int(11) DEFAULT NULL,
  `link` varchar(255) DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `is_read` (`is_read`),
  KEY `created_at` (`created_at`),
  KEY `idx_admin_notifications_unread` (`is_read`,`type`,`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for `admin_profiles`
DROP TABLE IF EXISTS `admin_profiles`;
CREATE TABLE `admin_profiles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `username` varchar(50) DEFAULT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `admin_role` varchar(50) DEFAULT 'admin',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `username` (`username`),
  CONSTRAINT `admin_profiles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `admin_profiles`
INSERT INTO `admin_profiles` (`id`, `user_id`, `username`, `full_name`, `admin_role`, `created_at`, `updated_at`) VALUES
(1, 1, 'admin', 'Admin Admin', 'admin', '2025-06-26 00:24:53', '2025-07-07 07:06:49');

-- Table structure for `appeal_history`
DROP TABLE IF EXISTS `appeal_history`;
CREATE TABLE `appeal_history` (
  `history_id` int(11) NOT NULL AUTO_INCREMENT,
  `appeal_id` int(11) NOT NULL,
  `previous_status` enum('pending','under_review','approved','rejected') DEFAULT NULL,
  `new_status` enum('pending','under_review','approved','rejected') NOT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `admin_response` text DEFAULT NULL,
  `changed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`history_id`),
  KEY `admin_id` (`admin_id`),
  KEY `idx_appeal_history` (`appeal_id`),
  KEY `idx_changed_at` (`changed_at`),
  CONSTRAINT `appeal_history_ibfk_1` FOREIGN KEY (`appeal_id`) REFERENCES `user_appeals` (`appeal_id`) ON DELETE CASCADE,
  CONSTRAINT `appeal_history_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `users` (`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `appeal_history`
INSERT INTO `appeal_history` (`history_id`, `appeal_id`, `previous_status`, `new_status`, `admin_id`, `admin_response`, `changed_at`, `notes`) VALUES
(14, 8, 'pending', 'approved', 1, '', '2025-07-14 13:08:20', 'Status changed from pending to approved by admin (ID: 1)'),
(15, 8, 'approved', 'approved', 1, NULL, '2025-07-14 13:08:20', 'Account restrictions removed and user status restored to active');

-- Table structure for `bookings`
DROP TABLE IF EXISTS `bookings`;
undefined;

-- Data for table `bookings`
INSERT INTO `bookings` (`booking_id`, `id`, `user_id`, `provider_id`, `package_id`, `service_type_id`, `pet_name`, `pet_type`, `cause_of_death`, `pet_image_url`, `booking_date`, `booking_time`, `status`, `special_requests`, `payment_method`, `payment_status`, `refund_id`, `delivery_option`, `delivery_address`, `delivery_distance`, `delivery_fee`, `total_price`, `price`, `created_at`, `updated_at`) VALUES
(8, 8, 3, 1, 10, 1, 'asasdasd', 'Dog', NULL, NULL, '2025-07-18 16:00:00', '10:00:00', 'completed', NULL, 'gcash', 'paid', NULL, 'delivery', 'Mariveles, Bataan', 0, '1860.00', '4183.00', '4183.00', '2025-07-14 17:26:01', '2025-07-14 17:31:16'),
(9, 9, 3, 1, 10, 1, 'pet', 'Dog', NULL, '/uploads/pets/pet_pet_3_1753060898837.png', '2025-07-25 16:00:00', '10:00:00', 'completed', NULL, 'gcash', 'paid', NULL, 'delivery', 'Mariveles, Bataan', 0, '1860.00', '4183.00', '4183.00', '2025-07-21 01:21:38', '2025-07-21 01:24:58'),
(10, 10, 3, 1, 10, 1, 'asdad', 'Cat', NULL, NULL, '2025-07-26 16:00:00', '10:00:00', 'completed', NULL, 'gcash', 'paid', NULL, 'delivery', 'Mariveles, Bataan', 0, '1860.00', '4183.00', '4183.00', '2025-07-21 01:48:49', '2025-07-21 16:00:12'),
(11, 11, 3, 1, 10, 1, 'sad', 'Bird', NULL, NULL, '2025-08-01 16:00:00', '10:00:00', 'cancelled', NULL, 'gcash', 'refunded', 4, 'pickup', NULL, 0, '0.00', '2323.00', '2323.00', '2025-07-21 01:57:38', '2025-07-21 01:59:32');

-- Table structure for `business_custom_options`
DROP TABLE IF EXISTS `business_custom_options`;
CREATE TABLE `business_custom_options` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider_id` int(11) NOT NULL,
  `option_type` enum('category','cremation_type','processing_time') NOT NULL,
  `option_value` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_business_custom_options_provider` (`provider_id`),
  KEY `idx_business_custom_options_type` (`option_type`),
  KEY `idx_business_custom_options_active` (`is_active`),
  CONSTRAINT `fk_business_custom_options_provider` FOREIGN KEY (`provider_id`) REFERENCES `service_providers` (`provider_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `business_custom_options`
INSERT INTO `business_custom_options` (`id`, `provider_id`, `option_type`, `option_value`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 1, 'category', 'Private', 1, '2025-07-21 07:19:16', '2025-07-21 07:19:16'),
(2, 1, 'category', 'Communal', 1, '2025-07-21 07:19:16', '2025-07-21 07:19:16'),
(3, 1, 'cremation_type', 'Standard', 1, '2025-07-21 07:19:16', '2025-07-21 07:19:16'),
(4, 1, 'cremation_type', 'Premium', 1, '2025-07-21 07:19:16', '2025-07-21 07:19:16'),
(5, 1, 'cremation_type', 'Deluxe', 1, '2025-07-21 07:19:16', '2025-07-21 07:19:16'),
(6, 1, 'processing_time', '1-2 days', 1, '2025-07-21 07:19:16', '2025-07-21 07:19:16'),
(7, 1, 'processing_time', '2-3 days', 1, '2025-07-21 07:19:16', '2025-07-21 07:19:16'),
(8, 1, 'processing_time', '3-5 days', 1, '2025-07-21 07:19:16', '2025-07-21 07:19:16');

-- Table structure for `business_notifications`
DROP TABLE IF EXISTS `business_notifications`;
CREATE TABLE `business_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` enum('info','success','warning','error') DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT 0,
  `link` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `business_notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for `business_pet_types`
DROP TABLE IF EXISTS `business_pet_types`;
CREATE TABLE `business_pet_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider_id` int(11) NOT NULL,
  `pet_type` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_business_pet_types_provider` (`provider_id`),
  KEY `idx_business_pet_types_active` (`is_active`),
  CONSTRAINT `fk_business_pet_types_provider` FOREIGN KEY (`provider_id`) REFERENCES `service_providers` (`provider_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `business_pet_types`
INSERT INTO `business_pet_types` (`id`, `provider_id`, `pet_type`, `is_active`, `created_at`, `updated_at`) VALUES
(9, 1, 'Dogs', 0, '2025-07-22 06:56:33', '2025-07-27 23:00:44'),
(10, 1, 'Cats', 0, '2025-07-22 06:56:33', '2025-07-27 23:00:44'),
(11, 1, 'Birds', 0, '2025-07-22 06:56:33', '2025-07-27 23:00:44'),
(12, 1, 'Rabbits', 0, '2025-07-22 06:56:33', '2025-07-27 23:00:44'),
(13, 1, 'Hamsters', 0, '2025-07-22 06:56:33', '2025-07-27 23:00:44'),
(14, 1, 'Dogs', 1, '2025-07-27 23:00:44', '2025-07-27 23:00:44'),
(15, 1, 'Cats', 1, '2025-07-27 23:00:44', '2025-07-27 23:00:44'),
(16, 1, 'Birds', 1, '2025-07-27 23:00:44', '2025-07-27 23:00:44'),
(17, 1, 'Rabbits', 1, '2025-07-27 23:00:44', '2025-07-27 23:00:44'),
(18, 1, 'Dogs', 1, '2025-07-27 23:00:44', '2025-07-27 23:00:44'),
(19, 1, 'Cats', 1, '2025-07-27 23:00:44', '2025-07-27 23:00:44'),
(20, 1, 'Birds', 1, '2025-07-27 23:00:44', '2025-07-27 23:00:44'),
(21, 1, 'Rabbits', 1, '2025-07-27 23:00:44', '2025-07-27 23:00:44');

-- Table structure for `business_profiles`
DROP TABLE IF EXISTS `business_profiles`;
undefined;

-- Data for table `business_profiles`
INSERT INTO `business_profiles` (`id`, `user_id`, `business_name`, `business_type`, `contact_first_name`, `contact_last_name`, `business_phone`, `business_address`, `business_hours`, `service_description`, `application_status`, `verification_status`, `verification_date`, `verification_notes`, `bir_certificate_path`, `business_permit_path`, `government_id_path`, `created_at`, `updated_at`) VALUES
(1, 2, 'Cremation', 'cremation', 'Justin', 'Sibonga', '+639163178412', 'Samal, Bataan', '9-10pm', 'asddssdsdadsdad', 'approved', 'approved', '2025-07-14 17:41:10', 'Application approved', '/uploads/documents/2/bir_certificate_1750371808943.png', '/uploads/documents/2/business_permit_1750371808764.png', '/uploads/documents/2/government_id_1750371808962.png', '2025-06-16 10:07:14', '2025-07-14 17:41:10');

-- Table structure for `email_log`
DROP TABLE IF EXISTS `email_log`;
CREATE TABLE `email_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `recipient` varchar(255) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message_id` varchar(255) DEFAULT NULL,
  `sent_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `recipient` (`recipient`),
  KEY `sent_at` (`sent_at`)
) ENGINE=InnoDB AUTO_INCREMENT=108 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `email_log`
INSERT INTO `email_log` (`id`, `recipient`, `subject`, `message_id`, `sent_at`) VALUES
(61, '<EMAIL>', '🚨 Account Restricted - Action Required', '<<EMAIL>>', '2025-07-14 13:06:50'),
(62, '<EMAIL>', '🚨 New Appeal Submitted - Action Required', '<<EMAIL>>', '2025-07-14 13:07:57'),
(63, '<EMAIL>', '🎉 Appeal Approved - Welcome Back!', '<<EMAIL>>', '2025-07-14 13:08:23'),
(64, '<EMAIL>', 'Booking Confirmation - Rainbow Paws', '<<EMAIL>>', '2025-07-14 17:26:05'),
(65, '<EMAIL>', '[Rainbow Paws] New Booking Received', '<<EMAIL>>', '2025-07-14 17:26:09'),
(66, '<EMAIL>', 'Booking Confirmed - Rainbow Paws', '<<EMAIL>>', '2025-07-14 17:30:25'),
(67, '<EMAIL>', 'Booking In progress - Rainbow Paws', '<<EMAIL>>', '2025-07-14 17:31:13'),
(68, '<EMAIL>', 'Booking Completed - Rainbow Paws', '<<EMAIL>>', '2025-07-14 17:31:19'),
(69, '<EMAIL>', 'New 5-Star Review Received - Rainbow Paws', '<<EMAIL>>', '2025-07-14 17:32:13'),
(70, '<EMAIL>', '🚨 Account Restricted - Action Required', '<<EMAIL>>', '2025-07-14 17:41:08'),
(71, '<EMAIL>', 'Reset Your Password', '<<EMAIL>>', '2025-07-20 20:30:38'),
(72, '<EMAIL>', 'Reset Your Password', '<<EMAIL>>', '2025-07-20 20:32:34'),
(73, '<EMAIL>', 'Booking Confirmation - Rainbow Paws', '<<EMAIL>>', '2025-07-21 01:21:42'),
(74, '<EMAIL>', '[Rainbow Paws] New Booking Received', '<<EMAIL>>', '2025-07-21 01:21:45'),
(75, '<EMAIL>', 'Booking Confirmed - Rainbow Paws', '<<EMAIL>>', '2025-07-21 01:24:48'),
(76, '<EMAIL>', 'Booking In progress - Rainbow Paws', '<<EMAIL>>', '2025-07-21 01:24:55'),
(77, '<EMAIL>', 'Booking Completed - Rainbow Paws', '<<EMAIL>>', '2025-07-21 01:25:00'),
(78, '<EMAIL>', 'Booking Confirmation - Rainbow Paws', '<<EMAIL>>', '2025-07-21 01:48:52'),
(79, '<EMAIL>', '[Rainbow Paws] New Booking Received', '<<EMAIL>>', '2025-07-21 01:48:55'),
(80, '<EMAIL>', 'Booking Confirmed - Rainbow Paws', '<<EMAIL>>', '2025-07-21 01:52:40'),
(81, '<EMAIL>', 'Booking Confirmation - Rainbow Paws', '<<EMAIL>>', '2025-07-21 01:57:41'),
(82, '<EMAIL>', '[Rainbow Paws] New Booking Received', '<<EMAIL>>', '2025-07-21 01:57:44'),
(83, '<EMAIL>', '[Rainbow Paws Admin] Refund Request - Booking Cancelled', '<<EMAIL>>', '2025-07-21 01:57:56'),
(84, '<EMAIL>', 'Refund Request Received - Rainbow Paws', '<<EMAIL>>', '2025-07-21 01:57:59'),
(85, '<EMAIL>', 'Booking Cancelled - Rainbow Paws', '<<EMAIL>>', '2025-07-21 01:58:01'),
(86, '<EMAIL>', '[Rainbow Paws] Booking Cancelled', '<<EMAIL>>', '2025-07-21 01:58:04'),
(87, '<EMAIL>', 'Booking Cancelled - Rainbow Paws', '<<EMAIL>>', '2025-07-21 01:58:08'),
(88, '<EMAIL>', 'Refund Completed - Rainbow Paws', '<<EMAIL>>', '2025-07-21 01:59:35'),
(89, '<EMAIL>', 'Welcome to Rainbow Paws! 🌈', '<<EMAIL>>', '2025-07-21 06:09:01'),
(90, '<EMAIL>', 'Your Verification Code - Rainbow Paws', '<<EMAIL>>', '2025-07-21 06:09:05'),
(91, '<EMAIL>', '[Rainbow Paws Admin] New Cremation Center Registration', '<<EMAIL>>', '2025-07-21 06:16:31'),
(92, '<EMAIL>', 'Welcome to Rainbow Paws! 🌈', '<<EMAIL>>', '2025-07-21 06:16:34'),
(93, '<EMAIL>', '[Rainbow Paws Admin] New Cremation Center Registration', '<<EMAIL>>', '2025-07-21 06:17:49'),
(94, '<EMAIL>', 'Welcome to Rainbow Paws! 🌈', '<<EMAIL>>', '2025-07-21 06:17:52'),
(95, '<EMAIL>', '[Rainbow Paws Admin] New Cremation Center Registration', '<<EMAIL>>', '2025-07-21 06:17:56'),
(96, '<EMAIL>', 'Welcome to Rainbow Paws! 🌈', '<<EMAIL>>', '2025-07-21 06:17:59'),
(97, '<EMAIL>', '[Rainbow Paws Admin] New Cremation Center Registration', '<<EMAIL>>', '2025-07-21 06:18:02'),
(98, '<EMAIL>', 'Welcome to Rainbow Paws! 🌈', '<<EMAIL>>', '2025-07-21 06:18:05'),
(99, '<EMAIL>', '[Rainbow Paws Admin] New Cremation Center Registration', '<<EMAIL>>', '2025-07-21 06:18:09'),
(100, '<EMAIL>', 'Welcome to Rainbow Paws! 🌈', '<<EMAIL>>', '2025-07-21 06:18:12'),
(101, '<EMAIL>', '[Rainbow Paws Admin] New Cremation Center Registration', '<<EMAIL>>', '2025-07-21 06:18:15'),
(102, '<EMAIL>', 'Welcome to Rainbow Paws! 🌈', '<<EMAIL>>', '2025-07-21 06:18:18'),
(103, '<EMAIL>', 'Booking In progress - Rainbow Paws', '<<EMAIL>>', '2025-07-21 16:00:08'),
(104, '<EMAIL>', 'Booking Completed - Rainbow Paws', '<<EMAIL>>', '2025-07-21 16:00:16'),
(105, '<EMAIL>', 'New 5-Star Review Received - Rainbow Paws', '<<EMAIL>>', '2025-07-27 14:24:03'),
(106, '<EMAIL>', 'Your Verification Code - Rainbow Paws', '<<EMAIL>>', '2025-07-28 08:04:51'),
(107, '<EMAIL>', 'New 5-Star Review Received - Rainbow Paws', '<<EMAIL>>', '2025-07-29 16:51:01');

-- Table structure for `email_queue`
DROP TABLE IF EXISTS `email_queue`;
CREATE TABLE `email_queue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `to_email` varchar(255) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `html` text NOT NULL,
  `text` text DEFAULT NULL,
  `from_email` varchar(255) DEFAULT NULL,
  `cc` varchar(255) DEFAULT NULL,
  `bcc` varchar(255) DEFAULT NULL,
  `attachments` text DEFAULT NULL,
  `status` enum('pending','sent','failed') NOT NULL DEFAULT 'pending',
  `attempts` int(11) NOT NULL DEFAULT 0,
  `error` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `sent_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `status` (`status`,`attempts`,`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for `migration_history`
DROP TABLE IF EXISTS `migration_history`;
CREATE TABLE `migration_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `migration_name` varchar(255) NOT NULL,
  `executed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `success` tinyint(1) DEFAULT 1,
  `error_message` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `migration_name` (`migration_name`),
  KEY `idx_migration_name` (`migration_name`),
  KEY `idx_executed_at` (`executed_at`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for `notifications`
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` enum('info','success','warning','error') DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT 0,
  `link` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`),
  KEY `user_id` (`user_id`),
  KEY `idx_notifications_user_unread` (`user_id`,`is_read`,`created_at`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=90 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `notifications`
INSERT INTO `notifications` (`id`, `user_id`, `title`, `message`, `type`, `is_read`, `link`, `created_at`) VALUES
(65, 1, 'New Appeal Submitted', 'undefined undefined has submitted an appeal: "TEST"', 'warning', 0, '/admin/users/cremation?appealId=8&userId=2', '2025-07-14 13:07:54'),
(86, 3, 'Service In Progress', 'The null for asdad is now in progress. You will be notified when it''s completed.', 'info', 1, '/user/furparent_dashboard/bookings?bookingId=10', '2025-07-21 16:00:04'),
(87, 3, 'Service Completed', 'The null for asdad has been completed. Thank you for choosing our services.', 'success', 1, '/user/furparent_dashboard/bookings?bookingId=10', '2025-07-21 16:00:12'),
(88, 2, 'New Review Received', 'Pet Parents left a 5-star review for your service.', 'info', 1, '/cremation/reviews', '2025-07-27 14:23:59'),
(89, 2, 'New Review Received', 'Pet Parents left a 5-star review for your service.', 'info', 1, '/cremation/reviews', '2025-07-29 16:50:57');

-- Table structure for `otp_attempts`
DROP TABLE IF EXISTS `otp_attempts`;
CREATE TABLE `otp_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `attempt_type` enum('generate','verify') NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `attempt_time` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `attempt_type` (`attempt_type`),
  KEY `attempt_time` (`attempt_time`),
  CONSTRAINT `otp_attempts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `otp_attempts`
INSERT INTO `otp_attempts` (`id`, `user_id`, `attempt_type`, `ip_address`, `attempt_time`) VALUES
(10, 3, 'generate', '::1', '2025-07-28 08:04:47'),
(11, 3, 'verify', '::1', '2025-07-28 08:05:15');

-- Table structure for `otp_codes`
DROP TABLE IF EXISTS `otp_codes`;
CREATE TABLE `otp_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `otp_code` varchar(10) NOT NULL,
  `expires_at` datetime NOT NULL,
  `is_used` tinyint(1) DEFAULT 0,
  `used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `otp_code` (`otp_code`),
  KEY `expires_at` (`expires_at`),
  KEY `is_used` (`is_used`),
  CONSTRAINT `otp_codes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `otp_codes`
INSERT INTO `otp_codes` (`id`, `user_id`, `otp_code`, `expires_at`, `is_used`, `used_at`, `created_at`) VALUES
(7, 3, '788870', '2025-07-28 08:14:47', 1, NULL, '2025-07-28 08:04:47');

-- Table structure for `package_addons`
DROP TABLE IF EXISTS `package_addons`;
CREATE TABLE `package_addons` (
  `addon_id` int(11) NOT NULL AUTO_INCREMENT,
  `package_id` int(11) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`addon_id`),
  KEY `package_id` (`package_id`),
  CONSTRAINT `package_addons_ibfk_1` FOREIGN KEY (`package_id`) REFERENCES `service_packages` (`package_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for `package_images`
DROP TABLE IF EXISTS `package_images`;
CREATE TABLE `package_images` (
  `image_id` int(11) NOT NULL AUTO_INCREMENT,
  `package_id` int(11) DEFAULT NULL,
  `image_path` varchar(255) DEFAULT NULL,
  `display_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`image_id`),
  KEY `package_id` (`package_id`),
  CONSTRAINT `package_images_ibfk_1` FOREIGN KEY (`package_id`) REFERENCES `service_packages` (`package_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `package_images`
INSERT INTO `package_images` (`image_id`, `package_id`, `image_path`, `display_order`, `created_at`) VALUES
(27, 13, '/uploads/packages/package_1_1753690779303.png', 1, '2025-07-28 08:19:45'),
(28, 13, '/uploads/packages/package_1_1753690782966.png', 2, '2025-07-28 08:19:45');

-- Table structure for `package_inclusions`
DROP TABLE IF EXISTS `package_inclusions`;
CREATE TABLE `package_inclusions` (
  `inclusion_id` int(11) NOT NULL AUTO_INCREMENT,
  `package_id` int(11) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`inclusion_id`),
  KEY `package_id` (`package_id`),
  CONSTRAINT `package_inclusions_ibfk_1` FOREIGN KEY (`package_id`) REFERENCES `service_packages` (`package_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `package_inclusions`
INSERT INTO `package_inclusions` (`inclusion_id`, `package_id`, `description`, `created_at`) VALUES
(42, 13, 'asdasd', '2025-07-28 08:19:45');

-- Table structure for `package_size_pricing`
DROP TABLE IF EXISTS `package_size_pricing`;
CREATE TABLE `package_size_pricing` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `package_id` int(11) NOT NULL,
  `size_category` enum('small','medium','large','extra_large') NOT NULL,
  `weight_range_min` decimal(8,2) DEFAULT NULL,
  `weight_range_max` decimal(8,2) DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_package_size_pricing_package` (`package_id`),
  KEY `idx_package_size_pricing_size` (`size_category`),
  CONSTRAINT `fk_package_size_pricing_package` FOREIGN KEY (`package_id`) REFERENCES `service_packages` (`package_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Table structure for `password_reset_tokens`
DROP TABLE IF EXISTS `password_reset_tokens`;
CREATE TABLE `password_reset_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `token` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` datetime NOT NULL,
  `is_used` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_token` (`token`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `password_reset_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `password_reset_tokens`
INSERT INTO `password_reset_tokens` (`id`, `user_id`, `token`, `created_at`, `expires_at`, `is_used`) VALUES
(7, 2, '386ce8313013ce7bb14e45345842a859b76437b8f0dd6ff96cde90a7880a0451', '2025-07-11 19:02:04', '2025-07-11 20:02:04', 1),
(8, 2, '79ca83432484e093858911daec847c40510d6746b54b02a7fd8123d9fde268e9', '2025-07-20 20:30:34', '2025-07-20 21:30:34', 1),
(9, 2, '9a2f36e4a8eaa70ede01ca89490439fe66e1069ab45e68702e23f06dce920ea6', '2025-07-20 20:32:31', '2025-07-20 21:32:31', 0);

-- Table structure for `payment_transactions`
DROP TABLE IF EXISTS `payment_transactions`;
CREATE TABLE `payment_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_id` int(11) NOT NULL,
  `payment_intent_id` varchar(255) DEFAULT NULL,
  `source_id` varchar(255) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) DEFAULT 'PHP',
  `payment_method` enum('gcash','cash') NOT NULL,
  `status` enum('pending','processing','succeeded','failed','cancelled') DEFAULT 'pending',
  `refund_id` int(11) DEFAULT NULL,
  `refunded_at` timestamp NULL DEFAULT NULL,
  `provider` enum('paymongo','manual') NOT NULL,
  `provider_transaction_id` varchar(255) DEFAULT NULL,
  `checkout_url` text DEFAULT NULL,
  `return_url` text DEFAULT NULL,
  `failure_reason` text DEFAULT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_booking_id` (`booking_id`),
  KEY `idx_source_id` (`source_id`),
  KEY `idx_payment_intent_id` (`payment_intent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_method` (`payment_method`),
  KEY `idx_refund_id` (`refund_id`),
  KEY `idx_payment_transactions_booking_status` (`booking_id`,`status`,`created_at`),
  KEY `idx_payment_transactions_analytics` (`payment_method`,`status`,`created_at`),
  KEY `idx_payment_transactions_refunds` (`refund_id`,`status`,`created_at`),
  CONSTRAINT `payment_transactions_ibfk_1` FOREIGN KEY (`booking_id`) REFERENCES `service_bookings` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Data for table `payment_transactions`
INSERT INTO `payment_transactions` (`id`, `booking_id`, `payment_intent_id`, `source_id`, `amount`, `currency`, `payment_method`, `status`, `refund_id`, `refunded_at`, `provider`, `provider_transaction_id`, `checkout_url`, `return_url`, `failure_reason`, `metadata`, `created_at`, `updated_at`) VALUES
(7, 8, NULL, 'src_AtvSeYvYCiKvvMQgbrtjHGis', '4183.00', 'PHP', 'gcash', 'succeeded', NULL, NULL, 'paymongo', NULL, 'https://secure-authentication.paymongo.com/sources?id=src_AtvSeYvYCiKvvMQgbrtjHGis', 'http://localhost:3000/payment/success?booking_id=8', NULL, '{"source_id":"src_AtvSeYvYCiKvvMQgbrtjHGis","customer_info":{"name":"Pet Parents","email":"<EMAIL>","phone":"+639163178412"}}', '2025-07-14 17:26:10', '2025-07-14 17:26:18'),
(8, 9, NULL, 'src_xzyYs8x1AzQeywEupffFqccP', '4183.00', 'PHP', 'gcash', 'succeeded', NULL, NULL, 'paymongo', NULL, 'https://secure-authentication.paymongo.com/sources?id=src_xzyYs8x1AzQeywEupffFqccP', 'http://localhost:3000/payment/success?booking_id=9', NULL, '{"source_id":"src_xzyYs8x1AzQeywEupffFqccP","customer_info":{"name":"Pet Parents","email":"<EMAIL>","phone":"+639163178412"}}', '2025-07-21 01:21:45', '2025-07-21 01:21:49'),
(9, 10, NULL, 'src_o9PKgg9c1HD3uXifpBKvMhgi', '4183.00', 'PHP', 'gcash', 'succeeded', NULL, NULL, 'paymongo', NULL, 'https://secure-authentication.paymongo.com/sources?id=src_o9PKgg9c1HD3uXifpBKvMhgi', 'http://localhost:3000/payment/success?booking_id=10', NULL, '{"source_id":"src_o9PKgg9c1HD3uXifpBKvMhgi","customer_info":{"name":"Pet Parents","email":"<EMAIL>","phone":"+639163178412"}}', '2025-07-21 01:48:56', '2025-07-21 01:49:24'),
(10, 11, NULL, 'src_AfqM4gEQaGvxseLCoa7oG8Cu', '2323.00', 'PHP', 'gcash', '', 4, '2025-07-21 01:59:32', 'paymongo', NULL, 'https://secure-authentication.paymongo.com/sources?id=src_AfqM4gEQaGvxseLCoa7oG8Cu', 'http://localhost:3000/payment/success?booking_id=11', NULL, '{"source_id":"src_AfqM4gEQaGvxseLCoa7oG8Cu","customer_info":{"name":"Pet Parents","email":"<EMAIL>","phone":"+639163178412"}}', '2025-07-21 01:57:44', '2025-07-21 01:59:32');

-- Table structure for `pets`
DROP TABLE IF EXISTS `pets`;
CREATE TABLE `pets` (
  `pet_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `species` varchar(100) DEFAULT NULL,
  `breed` varchar(255) DEFAULT NULL,
  `gender` enum('Male','Female') DEFAULT NULL,
  `age` varchar(50) DEFAULT NULL,
  `weight` decimal(8,2) DEFAULT NULL,
  `photo_path` varchar(255) DEFAULT NULL,
  `special_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`pet_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `pets_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `pets`
INSERT INTO `pets` (`pet_id`, `user_id`, `name`, `species`, `breed`, `gender`, `age`, `weight`, `photo_path`, `special_notes`, `created_at`, `updated_at`) VALUES
(5, 3, 'asasdasd', 'Dog', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-14 17:26:00', '2025-07-14 17:26:00'),
(6, 3, 'pet', 'Dog', NULL, NULL, NULL, NULL, '/uploads/pets/pet_pet_3_1753060898837.png', NULL, '2025-07-21 01:21:38', '2025-07-21 01:21:38'),
(7, 3, 'asdad', 'Cat', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 01:48:49', '2025-07-21 01:48:49'),
(8, 3, 'sad', 'Bird', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-21 01:57:38', '2025-07-21 01:57:38');

-- Table structure for `provider_availability`
DROP TABLE IF EXISTS `provider_availability`;
CREATE TABLE `provider_availability` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `is_available` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `provider_date_unique` (`provider_id`,`date`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `provider_availability`
INSERT INTO `provider_availability` (`id`, `provider_id`, `date`, `is_available`, `created_at`, `updated_at`) VALUES
(4, 1, '2025-07-21 16:00:00', 1, '2025-07-21 02:03:36', '2025-07-21 02:03:36'),
(5, 1, '2025-08-04 16:00:00', 1, '2025-07-27 12:29:50', '2025-07-27 12:29:50'),
(6, 1, '2025-08-06 16:00:00', 1, '2025-07-29 17:44:39', '2025-07-29 17:44:39'),
(7, 1, '2025-08-07 16:00:00', 1, '2025-07-29 17:44:51', '2025-07-29 17:44:51'),
(8, 1, '2025-07-29 16:00:00', 1, '2025-07-29 17:49:27', '2025-07-29 17:49:27');

-- Table structure for `provider_time_slots`
DROP TABLE IF EXISTS `provider_time_slots`;
CREATE TABLE `provider_time_slots` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `provider_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `available_services` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `provider_id` (`provider_id`,`date`)
) ENGINE=InnoDB AUTO_INCREMENT=549 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `provider_time_slots`
INSERT INTO `provider_time_slots` (`id`, `provider_id`, `date`, `start_time`, `end_time`, `available_services`, `created_at`, `updated_at`) VALUES
(382, 1, '2025-07-19 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(394, 1, '2025-08-30 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(395, 1, '2025-09-05 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(396, 1, '2025-09-06 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(397, 1, '2025-09-12 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(398, 1, '2025-09-13 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(399, 1, '2025-09-19 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(400, 1, '2025-09-20 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(401, 1, '2025-09-26 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(402, 1, '2025-09-27 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(403, 1, '2025-10-03 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(404, 1, '2025-10-04 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(405, 1, '2025-10-10 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(406, 1, '2025-10-11 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(407, 1, '2025-10-17 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(408, 1, '2025-10-18 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(409, 1, '2025-10-24 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(410, 1, '2025-10-25 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(420, 1, '2025-11-29 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(421, 1, '2025-12-05 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(422, 1, '2025-12-06 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(423, 1, '2025-12-12 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(424, 1, '2025-12-13 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(425, 1, '2025-12-19 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(426, 1, '2025-12-20 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(427, 1, '2025-12-26 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(428, 1, '2025-12-27 16:00:00', '10:00:00', '16:00:00', '[10]', '2025-07-14 17:24:11', '2025-07-14 17:24:11'),
(429, 1, '2025-07-21 16:00:00', '09:00:00', '10:00:00', '[10]', '2025-07-21 02:03:36', '2025-07-21 02:03:36'),
(431, 1, '2025-07-27 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(432, 1, '2025-07-28 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(434, 1, '2025-07-30 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(436, 1, '2025-08-03 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(438, 1, '2025-08-05 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(441, 1, '2025-08-10 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(442, 1, '2025-08-11 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(443, 1, '2025-08-12 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(444, 1, '2025-08-13 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(445, 1, '2025-08-14 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(446, 1, '2025-08-17 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(447, 1, '2025-08-18 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(448, 1, '2025-08-19 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(449, 1, '2025-08-20 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(450, 1, '2025-08-21 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(451, 1, '2025-08-24 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(452, 1, '2025-08-25 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(453, 1, '2025-08-26 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(454, 1, '2025-08-27 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(455, 1, '2025-08-28 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(456, 1, '2025-08-31 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(457, 1, '2025-09-01 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(458, 1, '2025-09-02 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(459, 1, '2025-09-03 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(460, 1, '2025-09-04 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(461, 1, '2025-09-07 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(462, 1, '2025-09-08 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(463, 1, '2025-09-09 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(464, 1, '2025-09-10 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(465, 1, '2025-09-11 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(466, 1, '2025-09-14 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(467, 1, '2025-09-15 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(468, 1, '2025-09-16 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(469, 1, '2025-09-17 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(470, 1, '2025-09-18 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(471, 1, '2025-09-21 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(472, 1, '2025-09-22 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(473, 1, '2025-09-23 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(474, 1, '2025-09-24 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(475, 1, '2025-09-25 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(476, 1, '2025-09-28 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(477, 1, '2025-09-29 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(478, 1, '2025-09-30 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(479, 1, '2025-10-01 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(480, 1, '2025-10-02 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(481, 1, '2025-10-05 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(482, 1, '2025-10-06 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(483, 1, '2025-10-07 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(484, 1, '2025-10-08 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(485, 1, '2025-10-09 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(486, 1, '2025-10-12 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(487, 1, '2025-10-13 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(488, 1, '2025-10-14 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(489, 1, '2025-10-15 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(490, 1, '2025-10-16 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(491, 1, '2025-10-19 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(492, 1, '2025-10-20 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(493, 1, '2025-10-21 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(494, 1, '2025-10-22 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(495, 1, '2025-10-23 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(496, 1, '2025-10-26 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(497, 1, '2025-10-27 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(498, 1, '2025-10-28 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(499, 1, '2025-10-29 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(500, 1, '2025-10-30 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(501, 1, '2025-11-02 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(502, 1, '2025-11-03 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(503, 1, '2025-11-04 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(504, 1, '2025-11-05 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(505, 1, '2025-11-06 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(506, 1, '2025-11-09 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(507, 1, '2025-11-10 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(508, 1, '2025-11-11 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(509, 1, '2025-11-12 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(510, 1, '2025-11-13 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(511, 1, '2025-11-16 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(512, 1, '2025-11-17 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(513, 1, '2025-11-18 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(514, 1, '2025-11-19 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(515, 1, '2025-11-20 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(516, 1, '2025-11-23 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(517, 1, '2025-11-24 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(518, 1, '2025-11-25 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(519, 1, '2025-11-26 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(520, 1, '2025-11-27 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(521, 1, '2025-11-30 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(522, 1, '2025-12-01 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(523, 1, '2025-12-02 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(524, 1, '2025-12-03 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(525, 1, '2025-12-04 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(526, 1, '2025-12-07 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(527, 1, '2025-12-08 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(528, 1, '2025-12-09 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(529, 1, '2025-12-10 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(530, 1, '2025-12-11 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(531, 1, '2025-12-14 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(532, 1, '2025-12-15 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(533, 1, '2025-12-16 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(534, 1, '2025-12-17 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(535, 1, '2025-12-18 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(536, 1, '2025-12-21 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(537, 1, '2025-12-22 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(538, 1, '2025-12-23 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(539, 1, '2025-12-24 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(540, 1, '2025-12-25 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(541, 1, '2025-12-28 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(542, 1, '2025-12-29 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(543, 1, '2025-12-30 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-28 08:18:36', '2025-07-28 08:18:36'),
(544, 1, '2025-08-06 16:00:00', '09:00:00', '10:00:00', '[13]', '2025-07-29 17:44:39', '2025-07-29 17:44:39'),
(545, 1, '2025-08-07 16:00:00', '09:00:00', '10:00:00', '[13]', '2025-07-29 17:44:51', '2025-07-29 17:44:51'),
(547, 1, '2025-08-04 16:00:00', '09:00:00', '17:00:00', '[13]', '2025-07-29 17:53:12', '2025-07-29 17:53:12'),
(548, 1, '2025-08-04 16:00:00', '21:00:00', '22:00:00', '[13]', '2025-07-29 17:53:12', '2025-07-29 17:53:12');

-- Table structure for `rate_limits`
DROP TABLE IF EXISTS `rate_limits`;
CREATE TABLE `rate_limits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(255) NOT NULL,
  `action` varchar(100) NOT NULL,
  `request_count` int(11) DEFAULT 1,
  `window_start` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_identifier_action` (`identifier`,`action`),
  KEY `idx_window_start` (`window_start`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for `refunds`
DROP TABLE IF EXISTS `refunds`;
CREATE TABLE `refunds` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `booking_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `reason` text DEFAULT NULL,
  `status` enum('pending','processing','processed','failed','cancelled') DEFAULT 'pending',
  `processed_by` int(11) DEFAULT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `transaction_id` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_booking_id` (`booking_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Data for table `refunds`
INSERT INTO `refunds` (`id`, `booking_id`, `amount`, `reason`, `status`, `processed_by`, `payment_method`, `transaction_id`, `notes`, `created_at`, `updated_at`) VALUES
(4, 11, '2323.00', 'Customer requested cancellation', 'processed', NULL, NULL, NULL, 'Refund request due to booking cancellation
PayMongo Error: Unable to locate the PayMongo payment record. This may indicate a payment data synchronization issue. Will retry automatically.', '2025-07-21 01:57:53', '2025-07-21 01:59:32');

-- Table structure for `reviews`
DROP TABLE IF EXISTS `reviews`;
CREATE TABLE `reviews` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `service_provider_id` int(11) NOT NULL,
  `booking_id` int(11) NOT NULL,
  `rating` int(11) NOT NULL,
  `comment` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `expiration_date` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_booking_review` (`booking_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Data for table `reviews`
INSERT INTO `reviews` (`id`, `user_id`, `service_provider_id`, `booking_id`, `rating`, `comment`, `created_at`, `updated_at`, `expiration_date`) VALUES
(5, 3, 1, 8, 5, 'great service', '2025-07-14 17:32:10', '2025-07-14 17:32:10', '2025-07-19 09:32:10'),
(6, 3, 1, 10, 5, NULL, '2025-07-27 14:23:59', '2025-07-27 14:23:59', '2025-08-01 06:23:59'),
(7, 3, 1, 9, 5, NULL, '2025-07-29 16:50:57', '2025-07-29 16:50:57', '2025-08-03 08:50:57');

-- Table structure for `service_bookings`
DROP TABLE IF EXISTS `service_bookings`;
CREATE TABLE `service_bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `provider_id` int(11) NOT NULL,
  `package_id` int(11) NOT NULL,
  `service_type_id` int(11) DEFAULT 1,
  `pet_name` varchar(255) DEFAULT NULL,
  `pet_type` varchar(100) DEFAULT NULL,
  `cause_of_death` text DEFAULT NULL,
  `pet_image_url` varchar(255) DEFAULT NULL,
  `booking_date` date DEFAULT NULL,
  `booking_time` time DEFAULT NULL,
  `status` enum('pending','confirmed','in_progress','completed','cancelled') DEFAULT 'pending',
  `special_requests` text DEFAULT NULL,
  `payment_method` varchar(50) DEFAULT 'cash',
  `payment_status` enum('not_paid','partially_paid','paid','refunded') DEFAULT 'not_paid',
  `refund_id` int(11) DEFAULT NULL,
  `delivery_option` enum('pickup','delivery') DEFAULT 'pickup',
  `delivery_address` text DEFAULT NULL,
  `delivery_distance` float DEFAULT 0,
  `delivery_fee` decimal(10,2) DEFAULT 0.00,
  `price` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_service_bookings_user` (`user_id`),
  KEY `idx_service_bookings_provider` (`provider_id`),
  KEY `idx_service_bookings_package` (`package_id`),
  KEY `idx_service_bookings_status` (`status`),
  KEY `idx_service_bookings_date` (`booking_date`),
  KEY `idx_refund_id` (`refund_id`),
  KEY `idx_service_bookings_user_dashboard` (`user_id`,`status`,`booking_date`),
  KEY `idx_service_bookings_provider_dashboard` (`provider_id`,`status`,`booking_date`),
  KEY `idx_service_bookings_admin_management` (`status`,`booking_date`,`created_at`),
  KEY `idx_service_bookings_payment` (`payment_status`,`status`,`created_at`),
  KEY `idx_service_type_id` (`service_type_id`),
  CONSTRAINT `fk_service_bookings_service_type` FOREIGN KEY (`service_type_id`) REFERENCES `service_types` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `service_bookings`
INSERT INTO `service_bookings` (`id`, `user_id`, `provider_id`, `package_id`, `service_type_id`, `pet_name`, `pet_type`, `cause_of_death`, `pet_image_url`, `booking_date`, `booking_time`, `status`, `special_requests`, `payment_method`, `payment_status`, `refund_id`, `delivery_option`, `delivery_address`, `delivery_distance`, `delivery_fee`, `price`, `created_at`, `updated_at`) VALUES
(8, 3, 1, 10, 1, 'asasdasd', 'Dog', NULL, NULL, '2025-07-18 16:00:00', '10:00:00', 'completed', NULL, 'gcash', 'paid', NULL, 'delivery', 'Mariveles, Bataan', 0, '1860.00', '4183.00', '2025-07-14 17:26:01', '2025-07-14 17:31:16'),
(9, 3, 1, 10, 1, 'pet', 'Dog', NULL, '/uploads/pets/pet_pet_3_1753060898837.png', '2025-07-25 16:00:00', '10:00:00', 'completed', NULL, 'gcash', 'paid', NULL, 'delivery', 'Mariveles, Bataan', 0, '1860.00', '4183.00', '2025-07-21 01:21:38', '2025-07-21 01:24:58'),
(10, 3, 1, 10, 1, 'asdad', 'Cat', NULL, NULL, '2025-07-26 16:00:00', '10:00:00', 'completed', NULL, 'gcash', 'paid', NULL, 'delivery', 'Mariveles, Bataan', 0, '1860.00', '4183.00', '2025-07-21 01:48:49', '2025-07-21 16:00:12'),
(11, 3, 1, 10, 1, 'sad', 'Bird', NULL, NULL, '2025-08-01 16:00:00', '10:00:00', 'cancelled', NULL, 'gcash', 'refunded', 4, 'pickup', NULL, 0, '0.00', '2323.00', '2025-07-21 01:57:38', '2025-07-21 01:59:32');

-- Table structure for `service_packages`
DROP TABLE IF EXISTS `service_packages`;
CREATE TABLE `service_packages` (
  `package_id` int(11) NOT NULL AUTO_INCREMENT,
  `provider_id` int(11) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `category` enum('Private','Communal') DEFAULT 'Private',
  `cremation_type` enum('Standard','Premium','Deluxe') DEFAULT 'Standard',
  `processing_time` varchar(50) DEFAULT '1-2 days',
  `price` decimal(10,2) DEFAULT NULL,
  `price_per_kg` decimal(10,2) DEFAULT 0.00,
  `delivery_fee_per_km` decimal(10,2) DEFAULT 0.00,
  `has_size_pricing` tinyint(1) DEFAULT 0,
  `uses_custom_options` tinyint(1) DEFAULT 0,
  `conditions` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`package_id`),
  KEY `idx_service_packages_provider` (`provider_id`),
  KEY `idx_service_packages_search` (`is_active`,`category`,`provider_id`,`name`),
  KEY `idx_service_packages_provider_active` (`provider_id`,`is_active`,`created_at`),
  CONSTRAINT `service_packages_ibfk_1` FOREIGN KEY (`provider_id`) REFERENCES `service_providers` (`provider_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `service_packages`
INSERT INTO `service_packages` (`package_id`, `provider_id`, `name`, `description`, `category`, `cremation_type`, `processing_time`, `price`, `price_per_kg`, `delivery_fee_per_km`, `has_size_pricing`, `uses_custom_options`, `conditions`, `is_active`, `created_at`, `updated_at`) VALUES
(13, 1, 'asdasd', 'asdasdasd', 'Private', 'Standard', '1-2 days', '21312.00', '11.00', '0.00', 0, 0, 'asdasda', 1, '2025-07-27 23:00:44', '2025-07-27 23:00:44');

-- Table structure for `service_providers`
DROP TABLE IF EXISTS `service_providers`;
CREATE TABLE `service_providers` (
  `provider_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `provider_type` enum('cremation','memorial','veterinary') DEFAULT NULL,
  `business_entity_type` enum('sole_proprietorship','corporation','partnership','limited_liability_company','cooperative') DEFAULT 'sole_proprietorship' COMMENT 'Legal business entity type for registration and compliance purposes',
  `contact_first_name` varchar(50) DEFAULT NULL,
  `contact_last_name` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `hours` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `application_status` enum('pending','declined','approved','restricted') DEFAULT 'pending',
  `verification_date` timestamp NULL DEFAULT NULL,
  `verification_notes` text DEFAULT NULL,
  `bir_certificate_path` varchar(255) DEFAULT NULL,
  `business_permit_path` varchar(255) DEFAULT NULL,
  `government_id_path` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`provider_id`),
  KEY `user_id` (`user_id`),
  KEY `idx_service_providers_type` (`provider_type`),
  CONSTRAINT `service_providers_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `service_providers`
INSERT INTO `service_providers` (`provider_id`, `user_id`, `name`, `provider_type`, `business_entity_type`, `contact_first_name`, `contact_last_name`, `phone`, `address`, `hours`, `description`, `application_status`, `verification_date`, `verification_notes`, `bir_certificate_path`, `business_permit_path`, `government_id_path`, `created_at`, `updated_at`) VALUES
(1, 2, 'Cremation', 'cremation', 'sole_proprietorship', 'Justin', 'Sibonga', '+639163178412', 'Samal, Bataan', '9-10pm', 'asddssdsdadsdad', 'approved', '2025-07-14 17:41:10', 'Application approved', '/uploads/documents/2/bir_certificate_1750371808943.png', '/uploads/documents/2/business_permit_1750371808764.png', '/uploads/documents/2/government_id_1750371808962.png', '2025-06-16 10:07:14', '2025-07-14 17:41:10');

-- Table structure for `service_types`
DROP TABLE IF EXISTS `service_types`;
CREATE TABLE `service_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `category` enum('cremation','memorial','veterinary','other') DEFAULT 'cremation',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_service_types_category` (`category`),
  KEY `idx_service_types_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Data for table `service_types`
INSERT INTO `service_types` (`id`, `name`, `description`, `category`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'Private Cremation', 'Individual cremation service for your beloved pet', 'cremation', 1, '2025-07-14 11:39:05', '2025-07-14 11:39:05'),
(2, 'Communal Cremation', 'Shared cremation service with other pets', 'cremation', 1, '2025-07-14 11:39:05', '2025-07-14 11:39:05'),
(3, 'Memorial Service', 'Memorial and remembrance services', 'memorial', 1, '2025-07-14 11:39:05', '2025-07-14 11:39:05'),
(4, 'Veterinary Consultation', 'Professional veterinary consultation', 'veterinary', 1, '2025-07-14 11:39:05', '2025-07-14 11:39:05'),
(5, 'Pet Transportation', 'Safe transportation services for pets', 'other', 1, '2025-07-14 11:39:05', '2025-07-14 11:39:05');

-- Table structure for `user_appeals`
DROP TABLE IF EXISTS `user_appeals`;
CREATE TABLE `user_appeals` (
  `appeal_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `user_type` enum('personal','business') NOT NULL DEFAULT 'personal',
  `business_id` int(11) DEFAULT NULL,
  `appeal_type` enum('restriction','suspension','ban') NOT NULL DEFAULT 'restriction',
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `evidence_files` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`evidence_files`)),
  `status` enum('pending','under_review','approved','rejected') NOT NULL DEFAULT 'pending',
  `admin_response` text DEFAULT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `submitted_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `reviewed_at` timestamp NULL DEFAULT NULL,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`appeal_id`),
  KEY `admin_id` (`admin_id`),
  KEY `idx_user_appeals` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_submitted_at` (`submitted_at`),
  CONSTRAINT `user_appeals_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `user_appeals_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `users` (`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `user_appeals`
INSERT INTO `user_appeals` (`appeal_id`, `user_id`, `user_type`, `business_id`, `appeal_type`, `subject`, `message`, `evidence_files`, `status`, `admin_response`, `admin_id`, `submitted_at`, `reviewed_at`, `resolved_at`, `created_at`, `updated_at`) VALUES
(8, 2, 'business', 1, 'restriction', 'TEST', 'asddasddasdd', '[]', 'approved', NULL, 1, '2025-07-14 13:07:54', NULL, '2025-07-14 13:08:20', '2025-07-14 13:07:54', '2025-07-14 13:08:20');

-- Table structure for `user_restrictions`
DROP TABLE IF EXISTS `user_restrictions`;
CREATE TABLE `user_restrictions` (
  `restriction_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `reason` text DEFAULT NULL,
  `restriction_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `duration` varchar(50) DEFAULT 'indefinite',
  `report_count` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`restriction_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `user_restrictions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Manages active and historical user account restrictions';

-- Data for table `user_restrictions`
INSERT INTO `user_restrictions` (`restriction_id`, `user_id`, `reason`, `restriction_date`, `duration`, `report_count`, `is_active`) VALUES
(25, 2, 'Restricted by admin', '2025-07-14 13:06:45', 'indefinite', 0, 0),
(26, 2, 'Restricted by admin', '2025-07-14 17:41:04', 'indefinite', 0, 0);

-- Table structure for `users`
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `gender` enum('Male','Female','Other') DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `role` enum('fur_parent','business','admin') NOT NULL DEFAULT 'fur_parent',
  `status` enum('active','inactive','suspended','restricted') DEFAULT 'active',
  `restriction_status` enum('none','restricted','suspended') DEFAULT 'none',
  `is_verified` tinyint(1) DEFAULT 0,
  `is_otp_verified` tinyint(1) DEFAULT 0,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `sms_notifications` tinyint(1) DEFAULT 1 COMMENT 'User preference for SMS notifications',
  `email_notifications` tinyint(1) DEFAULT 1 COMMENT 'User preference for email notifications',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_users_email` (`email`),
  KEY `idx_users_role` (`role`),
  KEY `idx_users_restriction_status` (`restriction_status`),
  KEY `idx_users_search_composite` (`role`,`status`,`first_name`,`last_name`,`email`),
  KEY `idx_users_auth_composite` (`email`,`status`,`is_verified`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Data for table `users`
INSERT INTO `users` (`user_id`, `email`, `password`, `first_name`, `last_name`, `phone`, `address`, `gender`, `profile_picture`, `role`, `status`, `restriction_status`, `is_verified`, `is_otp_verified`, `last_login`, `created_at`, `updated_at`, `sms_notifications`, `email_notifications`) VALUES
(1, '<EMAIL>', '$2b$10$/TMOT7juT/ytAoRAOjjP.uOu1ZpQiMYRVnvQP9UJLv/KC2CfLaxTe', 'Admin', 'Admin', '+639163178412', 'balanga, Bataan', 'Male', '/uploads/admin-profile-pictures/1/admin_profile_picture_1750985838993.png', 'admin', 'active', 'none', 1, 1, '2025-07-30 06:56:22', '2025-06-14 07:15:42', '2025-07-30 06:56:22', 1, 1),
(2, '<EMAIL>', '$2b$10$k1S.OTBJsw3va/1AwqkoEuZ8KpDN7aFrHHGtIjP2b2YBibW4Xnxuq', 'Justin', 'Sibonga', '+639163178412', 'Samal, Bataan', NULL, '/uploads/profile-pictures/2/profile_picture_1750949352242.png', 'business', 'active', 'none', 1, 1, '2025-07-29 19:59:44', '2025-06-16 10:07:10', '2025-07-29 19:59:44', 1, 1),
(3, '<EMAIL>', '$2b$10$FACNm48GgWanJsUCFaZW9OHTY1iHlokagC3hH5LCoPJ0Tr1ufssoa', 'Pet', 'Parents', '+639163178412', 'Mariveles, Bataan', NULL, '/uploads/profile-pictures/3/profile_picture_1752496830882.png', 'fur_parent', 'active', 'none', 1, 1, '2025-07-30 01:02:58', '2025-06-20 22:55:48', '2025-07-30 01:02:58', 1, 1);

SET FOREIGN_KEY_CHECKS = 1;
COMMIT;
