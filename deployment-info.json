{"name": "RainbowPaws", "version": "1.0.0", "buildDate": "2025-07-30T10:40:41.321Z", "nodeVersion": "v22.16.0", "environment": "production", "files": {"required": [".next/", "public/", "package.json", "next.config.js", ".env.local (create from .env.production)", "rainbow_paws_production.sql"], "optional": ["DEPLOYMENT.md", "README.md"]}, "commands": {"install": "npm install --production", "start": "npm start", "port": 3000}}